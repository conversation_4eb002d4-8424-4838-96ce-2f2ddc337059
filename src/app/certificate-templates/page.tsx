import { Metadata } from 'next';
import { SEOManager } from '@/lib/seo-manager';
import PageLayout from '@/components/layout/PageLayout';
import CertificateTemplatesPage from '@/components/certificate/CertificateTemplatesPage';

export const metadata: Metadata = {
  title: 'Certificate Templates | Free Professional Certificate Maker',
  description: 'Browse our collection of free professional certificate templates. Create achievement, completion, participation, and excellence certificates online. Instant PDF download, no registration required.',

  authors: [{ name: 'Certificate Maker Team' }],
  creator: 'Certificate Maker',
  publisher: 'Certificate Maker',
  metadataBase: new URL('https://certificatemaker.app'),
  alternates: {
    canonical: '/certificate-templates/',
  },
  openGraph: {
    title: 'Certificate Templates | Free Professional Certificate Maker',
    description: 'Browse our collection of free professional certificate templates. Create achievement, completion, participation, and excellence certificates online.',
    url: 'https://certificatemaker.app/certificate-templates/',
    siteName: 'Certificate Maker',
    type: 'website',
    images: [
      {
        url: '/og-certificate-templates.jpg',
        width: 1200,
        height: 630,
        alt: 'Professional Certificate Templates - Certificate Maker'
      }
    ],
    locale: 'en_US',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Certificate Templates | Free Professional Certificate Maker',
    description: 'Browse our collection of free professional certificate templates. Create achievement, completion, participation, and excellence certificates online.',
    images: ['/og-certificate-templates.jpg'],
    creator: '@certificatemaker',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
};

export default function CertificateTemplatesPageRoute() {
  return (
    <PageLayout className="bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* 结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'CollectionPage',
            name: 'Certificate Templates',
            description: 'Professional certificate templates for creating achievement, completion, participation, and excellence certificates online',
            url: 'https://certificatemaker.app/certificate-templates/',
            mainEntity: {
              '@type': 'ItemList',
              name: 'Certificate Template Categories',
              numberOfItems: 4,
              itemListElement: [
                {
                  '@type': 'ListItem',
                  position: 1,
                  item: {
                    '@type': 'CreativeWork',
                    name: 'Achievement Certificate Templates',
                    url: 'https://certificatemaker.app/certificate-templates/achievement-certificates/',
                    description: 'Professional achievement certificate templates for awards and recognition'
                  }
                },
                {
                  '@type': 'ListItem',
                  position: 2,
                  item: {
                    '@type': 'CreativeWork',
                    name: 'Completion Certificate Templates',
                    url: 'https://certificatemaker.app/certificate-templates/completion-certificates/',
                    description: 'Course and training completion certificate templates'
                  }
                },
                {
                  '@type': 'ListItem',
                  position: 3,
                  item: {
                    '@type': 'CreativeWork',
                    name: 'Participation Certificate Templates',
                    url: 'https://certificatemaker.app/certificate-templates/participation-certificates/',
                    description: 'Event and workshop participation certificate templates'
                  }
                },
                {
                  '@type': 'ListItem',
                  position: 4,
                  item: {
                    '@type': 'CreativeWork',
                    name: 'Excellence Certificate Templates',
                    url: 'https://certificatemaker.app/certificate-templates/excellence-certificates/',
                    description: 'Outstanding performance and excellence certificate templates'
                  }
                }
              ]
            },
            breadcrumb: {
              '@type': 'BreadcrumbList',
              itemListElement: [
                {
                  '@type': 'ListItem',
                  position: 1,
                  name: 'Home',
                  item: 'https://certificatemaker.app/'
                },
                {
                  '@type': 'ListItem',
                  position: 2,
                  name: 'Certificate Templates',
                  item: 'https://certificatemaker.app/certificate-templates/'
                }
              ]
            },
            publisher: {
              '@type': 'Organization',
              name: 'Certificate Maker',
              url: 'https://certificatemaker.app/'
            }
          })
        }}
      />

      <CertificateTemplatesPage />
    </PageLayout>
  );
}
