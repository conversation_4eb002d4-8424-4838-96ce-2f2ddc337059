import { Metadata } from 'next';
import { CertificateTemplate, TemplateCategory, CertificateCategory } from '@/types/certificate';
import { TemplateManager } from './template-manager';

/**
 * SEO管理器 - 统一管理所有页面的SEO配置
 */
export class SEOManager {
  private static readonly BASE_URL = 'https://certificatemaker.app';
  private static readonly SITE_NAME = 'Certificate Maker';
  private static readonly DEFAULT_IMAGE = '/og-image.jpg';

  /**
   * Generate homepage SEO metadata
   */
  static getHomePageMetadata(): Metadata {
    return {
      title: 'Free Certificate Maker - Create Professional PDF Certificates Online',
      description: 'Create beautiful, professional certificates online for free. Choose from 8 elegant templates across 4 categories, customize with your details, and download high-quality PDF certificates instantly.',
      openGraph: {
        title: 'Free Certificate Maker - Create Professional PDF Certificates Online',
        description: 'Create beautiful, professional certificates online for free. Choose from 8 elegant templates across 4 categories.',
        url: this.BASE_URL,
        siteName: this.SITE_NAME,
        images: [
          {
            url: this.DEFAULT_IMAGE,
            width: 1200,
            height: 630,
            alt: 'Certificate Maker - Professional Certificate Templates',
          },
        ],
        locale: 'en_US',
        type: 'website',
      },
      twitter: {
        card: 'summary_large_image',
        title: 'Free Certificate Maker - Create Professional PDF Certificates Online',
        description: 'Create beautiful, professional certificates online for free. Choose from 8 elegant templates across 4 categories.',
        images: [this.DEFAULT_IMAGE],
        creator: '@certificatemaker',
      },
      alternates: {
        canonical: this.BASE_URL,
      },
      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
          'max-video-preview': -1,
          'max-image-preview': 'large',
          'max-snippet': -1,
        },
      },
    };
  }

  /**
   * 生成分类页面SEO元数据
   */
  static getCategoryPageMetadata(category: TemplateCategory): Metadata {
    const templateCount = category.templates.length;
    const portraitCount = category.templates.filter(t => t.orientation === 'portrait').length;
    const landscapeCount = category.templates.filter(t => t.orientation === 'landscape').length;

    return {
      title: category.metaTitle,
      description: category.metaDescription,
      openGraph: {
        title: category.metaTitle,
        description: category.metaDescription,
        url: `${this.BASE_URL}/${category.urlSlug}/`,
        siteName: this.SITE_NAME,
        images: [
          {
            url: this.DEFAULT_IMAGE,
            width: 1200,
            height: 630,
            alt: `${category.displayName} Templates`,
          },
        ],
        locale: 'en_US',
        type: 'website',
      },
      twitter: {
        card: 'summary_large_image',
        title: category.metaTitle,
        description: category.metaDescription,
        images: [this.DEFAULT_IMAGE],
        creator: '@certificatemaker',
      },
      alternates: {
        canonical: `${this.BASE_URL}/${category.urlSlug}/`,
      },
      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
          'max-video-preview': -1,
          'max-image-preview': 'large',
          'max-snippet': -1,
        },
      },
      other: {
        'template-count': templateCount.toString(),
        'portrait-templates': portraitCount.toString(),
        'landscape-templates': landscapeCount.toString(),
      },
    };
  }

  /**
   * 生成模板页面SEO元数据
   */
  static getTemplatePageMetadata(template: CertificateTemplate, category: TemplateCategory): Metadata {
    return {
      title: template.seoTitle,
      description: template.seoDescription,
      openGraph: {
        title: template.seoTitle,
        description: template.seoDescription,
        url: `${this.BASE_URL}/${category.urlSlug}/${template.name}/`,
        siteName: this.SITE_NAME,
        images: [
          {
            url: template.preview,
            width: 800,
            height: template.orientation === 'landscape' ? 600 : 1067,
            alt: template.displayName,
          },
        ],
        locale: 'en_US',
        type: 'article',
      },
      twitter: {
        card: 'summary_large_image',
        title: template.seoTitle,
        description: template.seoDescription,
        images: [template.preview],
        creator: '@certificatemaker',
      },
      alternates: {
        canonical: `${this.BASE_URL}/${category.urlSlug}/${template.name}/`,
      },
      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
          'max-video-preview': -1,
          'max-image-preview': 'large',
          'max-snippet': -1,
        },
      },
      other: {
        'template-orientation': template.orientation,
        'template-category': category.displayName,
        'template-tags': template.tags.join(', '),
      },
    };
  }

  /**
   * 生成分类页面结构化数据
   */
  static getCategoryStructuredData(category: TemplateCategory) {
    return {
      '@context': 'https://schema.org',
      '@type': 'CollectionPage',
      name: category.displayName,
      description: category.description,
      url: `${this.BASE_URL}/${category.urlSlug}/`,
      mainEntity: {
        '@type': 'ItemList',
        numberOfItems: category.templates.length,
        itemListElement: category.templates.map((template, index) => ({
          '@type': 'ListItem',
          position: index + 1,
          item: {
            '@type': 'CreativeWork',
            '@id': `${this.BASE_URL}/${category.urlSlug}/${template.name}/`,
            name: template.displayName,
            description: template.description,
            url: `${this.BASE_URL}/${category.urlSlug}/${template.name}/`,
            image: `${this.BASE_URL}${template.preview}`,
            creator: {
              '@type': 'Organization',
              name: this.SITE_NAME,
              url: this.BASE_URL
            },
            keywords: template.seoKeywords.join(', '),
            category: category.displayName,
            additionalProperty: [
              {
                '@type': 'PropertyValue',
                name: 'orientation',
                value: template.orientation
              },
              {
                '@type': 'PropertyValue',
                name: 'aspectRatio',
                value: template.aspectRatio.toString()
              }
            ]
          }
        }))
      },
      breadcrumb: {
        '@type': 'BreadcrumbList',
        itemListElement: [
          {
            '@type': 'ListItem',
            position: 1,
            name: 'Home',
            item: this.BASE_URL
          },
          {
            '@type': 'ListItem',
            position: 2,
            name: category.displayName,
            item: `${this.BASE_URL}/${category.urlSlug}/`
          }
        ]
      },
      publisher: {
        '@type': 'Organization',
        name: this.SITE_NAME,
        url: this.BASE_URL
      }
    };
  }

  /**
   * 生成模板页面结构化数据
   */
  static getTemplateStructuredData(template: CertificateTemplate, category: TemplateCategory) {
    return {
      '@context': 'https://schema.org',
      '@type': 'CreativeWork',
      '@id': `${this.BASE_URL}/${category.urlSlug}/${template.name}/`,
      name: template.displayName,
      description: template.description,
      url: `${this.BASE_URL}/${category.urlSlug}/${template.name}/`,
      image: `${this.BASE_URL}${template.preview}`,
      creator: {
        '@type': 'Organization',
        name: this.SITE_NAME,
        url: this.BASE_URL
      },

      category: category.displayName,
      additionalProperty: [
        {
          '@type': 'PropertyValue',
          name: 'orientation',
          value: template.orientation
        },
        {
          '@type': 'PropertyValue',
          name: 'aspectRatio',
          value: template.aspectRatio.toString()
        },
        {
          '@type': 'PropertyValue',
          name: 'tags',
          value: template.tags.join(', ')
        }
      ],
      breadcrumb: {
        '@type': 'BreadcrumbList',
        itemListElement: [
          {
            '@type': 'ListItem',
            position: 1,
            name: 'Home',
            item: this.BASE_URL
          },
          {
            '@type': 'ListItem',
            position: 2,
            name: category.displayName,
            item: `${this.BASE_URL}/${category.urlSlug}/`
          },
          {
            '@type': 'ListItem',
            position: 3,
            name: template.displayName,
            item: `${this.BASE_URL}/${category.urlSlug}/${template.name}/`
          }
        ]
      },
      publisher: {
        '@type': 'Organization',
        name: this.SITE_NAME,
        url: this.BASE_URL
      }
    };
  }

  /**
   * 生成网站级别的结构化数据
   */
  static getWebsiteStructuredData() {
    return {
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      name: this.SITE_NAME,
      url: this.BASE_URL,
      description: 'Create professional certificates online for free with our easy-to-use certificate maker.',
      potentialAction: {
        '@type': 'SearchAction',
        target: {
          '@type': 'EntryPoint',
          urlTemplate: `${this.BASE_URL}/search?q={search_term_string}`
        },
        'query-input': 'required name=search_term_string'
      },
      publisher: {
        '@type': 'Organization',
        name: this.SITE_NAME,
        url: this.BASE_URL
      }
    };
  }
}
